import Link from "next/link";
import { FileText } from "@deemlol/next-icons";
import { ArrowRight } from "@deemlol/next-icons";

export function DocumentOptions() {
  const boxes = [
    {
      label: "Good Moral",
      href: "/",
      icon_File: FileText,
      icon_ArrowRight: ArrowRight,
    },
    {
      label: "Affidavit",
      href: "/",
      icon_File: FileText,
      icon_ArrowRight: ArrowRight,
    },
    {
      label: "clearance",
      href: "/",
      icon_File: FileText,
      icon_ArrowRight: ArrowRight,
    },
    {
      label: "License",
      href: "/",
      icon_File: FileText,
      icon_ArrowRight: ArrowRight,
    },
  ];

  return (
    <div className="flex justify-center items-center min-h-[calc(100vh-80px)]">
      <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {boxes.map((box, index) => (
          <Link
            key={index}
            href={box.href}
            className="h-30 w-30 bg-card shadow-md rounded-sm border flex flex-col justify-between p-2"
          >
            <div className="flex-1 flex items-center justify-center">
              <box.icon_File className="h-8 w-8" />
            </div>
            <div className="flex flex-col items-center gap-2">
              <h1 className="font-semibold">{box.label}</h1>
              <div className="w-full flex justify-end">
                <box.icon_ArrowRight className="h-6 w-6 text-chart-3" />
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
